FROM node:20-alpine

WORKDIR /app

# Create a non-root user that matches common host user IDs
RUN addgroup -g 1000 appgroup && \
    adduser -u 1000 -G appgroup -s /bin/sh -D appuser

# Install pnpm globally
RUN npm install -g pnpm@latest

# Change ownership of the app directory to appuser
RUN chown -R appuser:appgroup /app

# Switch to non-root user
USER appuser

# Copy package files and install dependencies
COPY --chown=appuser:appgroup package.json pnpm-lock.yaml ./
RUN pnpm install --frozen-lockfile

EXPOSE 3000

CMD ["pnpm", "dev"]
