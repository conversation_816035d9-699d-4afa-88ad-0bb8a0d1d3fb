FROM node:20-alpine

WORKDIR /app

# Install pnpm globally as root
RUN npm install -g pnpm@latest

# Create directories that will be used
RUN mkdir -p /app/static/compress_images

# Copy package files first (as root to avoid permission issues)
COPY package.json pnpm-lock.yaml ./

# Install dependencies as root to avoid permission issues
RUN pnpm install --frozen-lockfile

# Create a script to handle user creation and permission setup at runtime
RUN echo '#!/bin/sh' > /entrypoint.sh && \
    echo 'USER_ID=${USER_ID:-1000}' >> /entrypoint.sh && \
    echo 'GROUP_ID=${GROUP_ID:-1000}' >> /entrypoint.sh && \
    echo '' >> /entrypoint.sh && \
    echo '# Create group if it does not exist' >> /entrypoint.sh && \
    echo 'if ! getent group $GROUP_ID >/dev/null 2>&1; then' >> /entrypoint.sh && \
    echo '  addgroup -g $GROUP_ID appgroup' >> /entrypoint.sh && \
    echo 'fi' >> /entrypoint.sh && \
    echo '' >> /entrypoint.sh && \
    echo '# Create user if it does not exist' >> /entrypoint.sh && \
    echo 'if ! getent passwd $USER_ID >/dev/null 2>&1; then' >> /entrypoint.sh && \
    echo '  adduser -u $USER_ID -G $(getent group $GROUP_ID | cut -d: -f1) -s /bin/sh -D appuser' >> /entrypoint.sh && \
    echo 'fi' >> /entrypoint.sh && \
    echo '' >> /entrypoint.sh && \
    echo '# Fix permissions for app directory and static directory' >> /entrypoint.sh && \
    echo 'chown -R $USER_ID:$GROUP_ID /app' >> /entrypoint.sh && \
    echo 'chmod -R 755 /app/static' >> /entrypoint.sh && \
    echo '' >> /entrypoint.sh && \
    echo '# Switch to the user and execute the command' >> /entrypoint.sh && \
    echo 'exec su-exec $USER_ID:$GROUP_ID "$@"' >> /entrypoint.sh && \
    chmod +x /entrypoint.sh

# Install su-exec for user switching
RUN apk add --no-cache su-exec

EXPOSE 3000

ENTRYPOINT ["/entrypoint.sh"]
CMD ["pnpm", "dev"]
