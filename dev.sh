#!/bin/bash

# Ensure script stops on first error
set -e

# Check if podman-compose is installed
if ! command -v podman-compose &> /dev/null; then
    echo "podman-compose is not installed. Please install it first."
    exit 1
fi

# Get current user ID and group ID
USER_ID=$(id -u)
GROUP_ID=$(id -g)

# Export for docker-compose to use
export USER_ID
export GROUP_ID

echo "Starting development environment with hot reload..."
echo "Using USER_ID=$USER_ID and GROUP_ID=$GROUP_ID"

# Ensure the static directory exists and has correct permissions
mkdir -p static/compress_images
chmod 755 static/compress_images

# For SELinux systems, set appropriate context
if command -v getenforce &> /dev/null && [[ $(getenforce) != "Disabled" ]]; then
    echo "Setting SELinux context for volume mounts..."
    chcon -Rt container_file_t . 2>/dev/null || true
fi

# Check if .env file exists
if [ ! -f .env ]; then
    echo "Warning: .env file not found. Creating a basic one..."
    cat > .env << EOF
JWT_SECRET=supersecret-jwt-key-for-development-at-least-32-characters
POSTGRES_PASSWORD=postgres
POSTGRES_USER=postgres
PORT=3000
EOF
fi

# Start development environment with hot reload
podman-compose -f docker-compose.dev.yaml up --build

# This script will be interrupted by Ctrl+C, which will trigger podman-compose down
# The following will only execute if podman-compose up exits normally
echo "Stopping development environment..."
podman-compose -f docker-compose.dev.yaml down
