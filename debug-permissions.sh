#!/bin/bash

echo "=== Debug Permissions Script ==="
echo "This script helps diagnose permission issues with Podman containers"
echo

# Check current user info
echo "1. Host User Information:"
echo "   Current User: $(whoami)"
echo "   User ID: $(id -u)"
echo "   Group ID: $(id -g)"
echo "   Groups: $(groups)"
echo

# Check Podman setup
echo "2. Podman Information:"
if command -v podman &> /dev/null; then
    echo "   Podman version: $(podman --version)"
    echo "   Podman info:"
    podman info --format "   Storage Driver: {{.Store.GraphDriverName}}"
    podman info --format "   Storage Root: {{.Store.GraphRoot}}"
    podman info --format "   Rootless: {{.Host.Security.Rootless}}"
else
    echo "   Podman not found!"
fi
echo

# Check SELinux
echo "3. SELinux Status:"
if command -v getenforce &> /dev/null; then
    echo "   SELinux status: $(getenforce)"
    if [[ $(getenforce) != "Disabled" ]]; then
        echo "   Current context: $(ls -Z . | head -1 | awk '{print $1}')"
    fi
else
    echo "   SELinux not available"
fi
echo

# Check directory permissions
echo "4. Directory Permissions:"
echo "   Current directory: $(pwd)"
echo "   Permissions: $(ls -ld . | awk '{print $1, $3, $4}')"
if [ -d "static" ]; then
    echo "   Static directory: $(ls -ld static | awk '{print $1, $3, $4}')"
    if [ -d "static/compress_images" ]; then
        echo "   Compress images dir: $(ls -ld static/compress_images | awk '{print $1, $3, $4}')"
    fi
else
    echo "   Static directory: Not found"
fi
echo

# Check if podman-compose is available
echo "5. Podman Compose:"
if command -v podman-compose &> /dev/null; then
    echo "   podman-compose version: $(podman-compose --version)"
else
    echo "   podman-compose not found!"
    echo "   Install with: pip3 install podman-compose"
fi
echo

# Check environment file
echo "6. Environment Configuration:"
if [ -f ".env" ]; then
    echo "   .env file exists"
    echo "   Contents (without sensitive data):"
    grep -v "PASSWORD\|SECRET" .env | sed 's/^/   /'
else
    echo "   .env file not found!"
fi
echo

# Check for common issues
echo "7. Common Issues Check:"

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    echo "   ⚠️  WARNING: Running as root user"
    echo "      Consider using rootless Podman for better security"
fi

# Check if user is in podman group (for rootful podman)
if groups | grep -q podman; then
    echo "   ✅ User is in podman group"
else
    echo "   ⚠️  User not in podman group (may be normal for rootless)"
fi

# Check subuid/subgid for rootless
if [ -f "/etc/subuid" ] && [ -f "/etc/subgid" ]; then
    if grep -q "$(whoami)" /etc/subuid && grep -q "$(whoami)" /etc/subgid; then
        echo "   ✅ Subuid/subgid configured for rootless Podman"
    else
        echo "   ⚠️  Subuid/subgid not configured for $(whoami)"
        echo "      Run: sudo usermod --add-subuids 100000-165535 --add-subgids 100000-165535 $(whoami)"
    fi
fi

echo
echo "=== Suggested Actions ==="
echo

if [ ! -d "static/compress_images" ]; then
    echo "• Create static directory: mkdir -p static/compress_images"
fi

if [ ! -f ".env" ]; then
    echo "• Create .env file with required variables"
fi

if ! command -v podman-compose &> /dev/null; then
    echo "• Install podman-compose: pip3 install podman-compose"
fi

echo "• Try running: ./dev.sh"
echo "• If issues persist, try: ./dev-rootless.sh"
echo
