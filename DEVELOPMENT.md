# Development Environment Setup

## Vấn đề đã được khắc phục

### 1. **User Mapping Issues**
- **Vấn đề cũ**: Xung đột giữa user được tạo trong Dockerfile (UID 1000) và user mapping từ host
- **Giải pháp**: Sử dụng entrypoint script để tạo user động dựa trên USER_ID/GROUP_ID từ host

### 2. **Permission Issues**
- **Vấn đề cũ**: Container không thể ghi vào volume mounts do quyền không khớp
- **Giải pháp**: Entrypoint script tự động fix permissions cho /app và /app/static

### 3. **SELinux Context**
- **Vấn đề cũ**: Volume mounts bị chặn bởi SELinux
- **Giải pháp**: Tự động set SELinux context trong dev.sh

## Cách sử dụng

### 1. **Kiể<PERSON> tra hệ thống**
```bash
./debug-permissions.sh
```

### 2. **Chạy development environment**
```bash
./dev.sh
```

### 3. **Nếu vẫn có vấn đề với rootless Podman**
```bash
./dev-rootless.sh
```

## Các thay đổi chính

### Dockerfile.dev
- Loại bỏ việc tạo user cố định
- Thêm entrypoint script để tạo user động
- Sử dụng su-exec để switch user an toàn
- Cài đặt dependencies as root để tránh permission issues

### docker-compose.dev.yaml
- Loại bỏ `user:` directive (được xử lý bởi entrypoint)
- Thêm USER_ID/GROUP_ID vào environment variables
- Thêm build args để pass user info

### dev.sh
- Thêm tạo static directory tự động
- Thêm SELinux context handling
- Thêm kiểm tra .env file
- Cải thiện error handling

## Troubleshooting

### Lỗi Permission Denied
```bash
# Kiểm tra permissions
ls -la static/
ls -la static/compress_images/

# Fix permissions manually nếu cần
sudo chown -R $(id -u):$(id -g) static/
chmod -R 755 static/
```

### Lỗi SELinux
```bash
# Kiểm tra SELinux status
getenforce

# Set context cho directory
sudo chcon -Rt container_file_t .
```

### Lỗi Subuid/Subgid (Rootless Podman)
```bash
# Kiểm tra subuid/subgid
grep $(whoami) /etc/subuid /etc/subgid

# Nếu không có, thêm vào:
sudo usermod --add-subuids 100000-165535 --add-subgids 100000-165535 $(whoami)
```

### Container không start
```bash
# Xem logs chi tiết
podman-compose -f docker-compose.dev.yaml logs api
podman-compose -f docker-compose.dev.yaml logs worker

# Rebuild từ đầu
podman-compose -f docker-compose.dev.yaml down
podman-compose -f docker-compose.dev.yaml build --no-cache
```

## Kiến trúc mới

```
Host User (UID:GID) 
    ↓
Environment Variables (USER_ID, GROUP_ID)
    ↓
Docker Compose (pass to container)
    ↓
Entrypoint Script (create matching user)
    ↓
su-exec (switch to user)
    ↓
Application (runs as correct user)
```

## Lợi ích

1. **Flexible User Mapping**: Hoạt động với bất kỳ UID/GID nào từ host
2. **Automatic Permission Fixing**: Tự động fix permissions khi container start
3. **SELinux Compatible**: Tự động handle SELinux context
4. **Better Error Handling**: Script kiểm tra và báo lỗi rõ ràng hơn
5. **Debug Support**: Script debug-permissions.sh để chẩn đoán vấn đề
