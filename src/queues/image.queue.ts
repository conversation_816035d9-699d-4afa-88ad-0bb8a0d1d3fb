import { Queue, type QueueOptions } from "bullmq";
import { redisConnection } from "../config";
import { JOB_NAMES, QUEUE_NAMES } from "../constants/job-names";
import type { CompressImageJobData } from "../types/image.types";

const defaultQueueOptions: QueueOptions = {
  connection: redisConnection,
  defaultJobOptions: {
    attempts: 3,
    backoff: {
      type: "exponential",
      delay: 1000,
    },
  },
};

export const imageQueue = new Queue(QUEUE_NAMES.IMAGE, defaultQueueOptions);

export const addCompressImageJob = async (data: CompressImageJobData) => {
  imageQueue.add(JOB_NAMES.IMAGE.COMPRESS, data);
};
